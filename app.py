import requests
from requests.auth import HTTPBasicAuth
import json, os
import time
import datetime
# from datetime import datetime
from datetime import date
import gspread,os
from oauth2client.service_account import ServiceAccountCredentials
import base64
from datetime import timedelta

time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
time_one_minute_ago = (datetime.datetime.now() - datetime.timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M")

if os.name=='posix':
	linuxDir = '/home/<USER>/files/'
else:
	linuxDir = ''

sheet_url = 'https://docs.google.com/spreadsheets/d/1x8MXW7VfNjufzi0pXeIaXgg5LBUyNc24uWb1X8P--X0/'
scope2 = ['https://spreadsheets.google.com/feeds','https://www.googleapis.com/auth/drive']
creds = ServiceAccountCredentials.from_json_keyfile_name(f'{linuxDir}abc.json', scope2)
# creds = ServiceAccountCredentials.from_json_keyfile_name(f'abc.json', scope2)
client = gspread.authorize(creds)
sheet = client.open_by_url(sheet_url)
worksheet = sheet.get_worksheet(0)
sucess=0

session = requests.Session()
username = 'script'
password = 'createpage'
confUrl = 'https://art.iyc.ishafoundation.org'
confcwUrl = 'https://art.iyc.ishafoundation.org/rest/cw/1/content'
Statuses = ['Transcription', 'Transcribed', 'I Proofed', 'II Proofed', 'Proof-Read']
TrStatuses = ['Transcription', 'I Proofing', 'II Proofing', 'Proof-Reading']

jira_url = "https://servicedesk.isha.in"
EMAIL = 'archives.script' 
API_TOKEN = "@utomate"
decoded_str = base64.b64decode('YXJjaGl2ZXMuc2NyaXB0OkB1dG9tYXRl').decode('utf-8')
Cusername, Cpassword = decoded_str.split(":")
auth = HTTPBasicAuth(Cusername, Cpassword)
api_url = f"{jira_url}/rest/api/2/search"

CHECK_VALIDITY_PERIOD = 2 # minutes

def get_viewers_list(page_id):
	url = f"{confUrl}/rest/api/content/{page_id}/restriction/byOperation"
	res=[]
	response = requests.get(
		url,
		auth=HTTPBasicAuth(username, password)
		# ,headers={"Accept": "application/json"}
	)
	if response.status_code == 200:
		restrictions_data = response.json()
		lst = restrictions_data['update']['restrictions']['user']['results']
		for user in lst:
			subject_name = user['username']
			res.append(subject_name)
	# else:
		# print(f"Failed to fetch page restrictions. Status code: {response.status_code}")
	return res

def fetch_all_issues(query,stime=time.time()):
	all_issues = []
	max_results = 500 # experimental
	start_at = 0
	while True:
		# print('querying')
		params = {
			'jql': query,  # JQL query to filter issues
			'startAt': start_at,  # Pagination: start point
			'maxResults': max_results,  # Maximum results per page
			'fields': 'key,summary,components,status,assignee,customfield_111118,customfield_110905,customfield_110909,customfield_110906,customfield_110910,customfield_110907,customfield_110911,customfield_110908,customfield_110912'
		}
		tryflag=1
		maxtries=0
		while tryflag==1:
			if maxtries>20:
				break
			response = session.get(api_url, params=params, auth=auth)
			time.sleep(0.2)
			if response.status_code == 200:
				tryflag=0
			else:
				maxtries+=1
				print('in fetch, pausing')
				time.sleep(5)
				
		# response = session.get(api_url, params=params, auth=auth)
		# if response.status_code != 200:
			# print(f"Failed to fetch issues: {response.status_code} - {response.text}")
			# break
		data = response.json()
		all_issues.extend(data['issues'])

		if start_at + max_results >= data['total']:
			# print(f"done {ct}/44, got {data['total']} issues, in {int(time.time()-stime)} s. Time:{int(time.time()-begintime)} s")
			break
		start_at += max_results
	return all_issues

def make_request(url):
	tryflag=1
	while tryflag==1:
		while True:
			try:
				response = requests.get(
					url,
					auth=auth,
					headers={"Accept": "application/json"}
				)
				time.sleep(0.5)
				break
			except ConnectionError:
				print('------------------------------ got connection error')
				time.sleep(3)
		if response.status_code == 200:
			tryflag=0
		else:
			print('------------------------------ in fetch, pausing')
			time.sleep(5)
	return response

d2 = datetime.datetime.fromtimestamp(time.time())

def check_validity(key):
	url = f"{jira_url}/rest/api/2/issue/{key}?expand=changelog"
	response = make_request(url)
	changelog = response.json()

	valid=0
	for history in reversed(changelog['changelog']['histories']):
		time_string = history['created'].split('.')[0]
		d1 = datetime.datetime.fromisoformat(time_string)
		# d2 = datetime.datetime.fromtimestamp(time.time())
		diff = int((d2-d1).total_seconds() / 60)
		print('checking validity-',key,diff)
		if diff>CHECK_VALIDITY_PERIOD:
			break
		for item in history['items']:
			fieldchanged = item['field']
			if 'IE_' not in fieldchanged:
				return 1
	return valid

def get_jira_data():
	query = f'project = "Ar Transcription" and type=Task and updated >= "{time_one_minute_ago}" and updated < "{time_now}"'
	print(query)
	# exit()
	# query = 'key = "AT-53003"'
	issues = fetch_all_issues(query)
	res=[]
	for issue in issues:
		# if check_validity(issue['key'])==1:
		# 	res.append(issue)
		res.append(issue)
	return res

def get_current_conf_data(page_id):
	# getting current status
	url = f'{confcwUrl}/{page_id}/status?expand=approvals'
	while True:
		response = requests.get(url, auth=HTTPBasicAuth(username, password))
		page = response.json()
		if response.status_code==200:
			break
		else:
			time.sleep(1)

	curStatus = page['state']['name']

	# getting current approver
	approver=''
	if len(page['approvals']):
		approvers = page['approvals'][0]['approvers'];
		# print(approvers)
		if approvers and len(approvers) and approvers[0]['user']:
			approver = approvers[0]['user']['name']
		# else:
			# approver = 'none'
	return [curStatus, approver]

def set_conf_status(number, page_id):
	global success
	Statuses = ['Transcription', 'Transcribed', 'I Proofed', 'II Proofed', 'Proof-Read', 'On Hold']
	url = f'{confcwUrl}/{page_id}/state?expand=state&admin=true'
	data = {
		'name': Statuses[4]
	}
	# do twice, so that assignee is removed in the first one automatically
	update_response = requests.put(
		url,
		data=json.dumps(data),
		auth=HTTPBasicAuth(username, password),
		headers={'Content-Type': 'application/json'}
	)
	if update_response.status_code!=200:
		# global success
		success=-1
		print('problem w setting status')
		return
	if number==5:
		return

	data = {
		'name': Statuses[number-1]
	}
	# response = requests.get(url, auth=HTTPBasicAuth(username, password))
	# request = { ...baseReq, method: 'PUT', body: JSON.stringify(data) }; // PATCH

	# try:
	update_response = requests.put(
		url,
		data=json.dumps(data),
		auth=HTTPBasicAuth(username, password),
		headers={'Content-Type': 'application/json'}
	)
	# except:
	# 	global success
	# 	success=-1
	# 	pass
	# print('confluence status set:',update_response.status_code)
	if update_response.status_code!=200:
		# global success
		success=-1
		print('problem w setting status')

def set_conf_assignee(number, assignee, page_id):
	TrStatuses = ['Transcription', 'I Proofing', 'II Proofing', 'Proof-Reading']
	url = f'{confcwUrl}/{page_id}/approvals/assign?expand=state,states,actions,approvals&admin=true'
	data = {
		'name': TrStatuses[number-1],
		'assignees': [{'username': f'{assignee}'}] }
	# try:
	update_response = requests.patch(
		url,
		data=json.dumps(data),
		auth=HTTPBasicAuth(username, password),
		headers={'Content-Type': 'application/json'}
	)
	# except:
	# 	global success
	# 	success=-1
	# 	print('not success')
	# return
	# print('confluence assignee set:',update_response.status_code)
	if update_response.status_code!=200:
		global success
		success=-1
		print('problem w setting assignee')

def get_page_id(res, title):
	url = f'{confUrl}/rest/api/content/?title={title}'
	response = requests.get(url, auth=HTTPBasicAuth(username, password))
	page = response.json()
	try:
		return page['results'][0]['id']
	except:
		print('could not get conf page id from jira ticket:',res['key'])
		return -1

def change_or_no(conf, jira):
	confstatus=conf[0].lower()
	confassignee=conf[1]
	jiraassignee=jira[1]
	jirastatus=''
	if jira[0].lower() in ['paused','transcript not applicable']:
		return 0
		
	if jira[0] in ['open','assigned tr','tr in progress'] :
		jirastatus='transcription'
	if jira[0] in ['transcribed','assigned ip','ip in progress'] :
		jirastatus='transcribed'
	if jira[0] in ['i proofed','assigned iip','iip in progress'] :
		jirastatus='i proofed'
	if jira[0] in ['ii proofed','assigned pr','pr in progress'] :
		jirastatus='ii proofed'
	if jira[0] in ['proof-read'] :
		jirastatus='proof-read'
	if jira[0] in ['on hold'] :
		jirastatus='on hold'

	if jirastatus==confstatus and jiraassignee==confassignee:
		return 0
	else:
		return 1

def getJiraField(field_name):
	global m
	if len(m.keys())==0:
		# print('----running---')
		lines = open(f'{linuxDir}jira-all fields names-NEW.txt','r',encoding='utf-8').read().split('\n')
		for line in lines:
			t = line.split(' ,,, ')
			m[t[1].lower()]=t[0]
			# if t[1]=='AT Transcriber':
				# print('yes')
		# print(m.keys())
		# exit()
	return m[field_name.lower()]

def do_remove_assignee_process(status, issue, page_id):
	# print('in do remove assignee process')
	
	flist_names = ['AT_EngTranscriber'
		,'AT_TamTranscriber'
		,'AT_EngProofer1'
		,'AT_TamProofer1'
		,'AT_EngProofer2'
		,'AT_TamProofer2'
		,'AT_EngProofReader'
		,'AT_TamProofReader'
		]
	fieldsList=['customfield_110905', 'customfield_110909', 'customfield_110906', 'customfield_110910', 'customfield_110907', 'customfield_110911', 'customfield_110908', 'customfield_110912']
	till=0

	if status in ['transcribed', 'assigned ip', 'ip in progress']:
		till = 2
	elif status in ['i proofed', 'assigned iip', 'iip in progress']:
		till = 4
	elif status in ['ii proofed', 'assigned pr', 'pr in progress']:
		till = 6
	elif status in ['proof-read']:
		till = 8
	else:
		return ''

	users = []

	for i in fieldsList[:till]:
		# print(i, issue['fields'][i])
		if issue['fields'][i]:
			t = issue['fields'][i]
			for ti in t:
				users.append(ti['name'])
	# print('---> users = ',users)
	if len(users)==0:
		return ''
	
	ruser=[]
	viewers=get_viewers_list(page_id)
	# print('viewers-',viewers)
	for user in users:
		if user in viewers:
			# print('removing-',user)
			ruser.append( removeuser(user,page_id))

	if len(ruser)>=1:
		return ', '.join(ruser)
	else:
		return ''

def removeuser(user, pgId):
	url = f'https://gnanetra.iyc.ishafoundation.org/ArApi/conf/rm-perm?pid={pgId}&user={user}'
	try:
		resp = requests.post(url)
	except:
		return 'error'
	if resp.status_code==200:
		return user
	else:
		return 'error'

def update_confluence(res):
	global success
	global totalupdates
	possible_statuses=['open', # 0
	'assigned tr', # 1
	'tr in progress', # 2
	'transcribed', # 3
	'assigned ip', # 4
	'ip in progress', # 5
	'i proofed', # 6
	'assigned iip', # 7
	'iip in progress', # 8
	'ii proofed', # 9
	'assigned pr', # 10
	'pr in progress', # 11
	'proof-read'] # 12
	printflag=0

	success=1
	title=res['fields']['customfield_111118'].split('/')[-1]
	jiratitle=res['fields']['summary']
	status=res['fields']['status']['name'].lower()
	if res['fields']['assignee']:
		assignee=res['fields']['assignee']['name']
	else:
		assignee=''
	key = res['key']
	#if assignee=='satyasree.a':
	#	assignee = 'satyasree.akula'

	
	page_id = get_page_id(res, title)
	if page_id==-1:
		return
	curconf = get_current_conf_data(page_id)
	changeflag = change_or_no(curconf,[status,assignee])
	# print(status,assignee,'-----',curconf, changeflag)
	if changeflag==0:
		return

	# offline log - need to change	
	fc = open(os.getcwd()+'/needtochange.txt','a',encoding='utf-8')
	fc.write(f'{time_now}\t{key}\t{status}\t{assignee}\n')
	fc.close()

	# TESTING
	# return

	### REMOVE VIEW PERMISSION FOR PREVIOUS USERS
	# IS TRAINING - PATCH
	removedassignee=''
	components = res['fields']['components']
	is_training=0
	for component in components:
		if component['name']=='Training':
			is_training=1
			break
	if is_training==0:
		removedassignee = do_remove_assignee_process(status, res, page_id)
	###



	print(f'updating conf. jira: [{key} {title[:20]}...] status=\'{status}\', assignee=\'{assignee}\'')
	if status=='open':
		if printflag: print('here1')
		set_conf_status(1, page_id)
	elif status=='assigned tr' or status=='tr in progress':
		if printflag: print('here2')
		set_conf_status(1, page_id)
		if assignee: set_conf_assignee(1, assignee, page_id)
	elif status=='transcribed':
		if printflag: print('here3')
		set_conf_status(2, page_id)
	elif status=='assigned ip' or status=='ip in progress':
		if printflag: print('here4')
		set_conf_status(2, page_id)
		if assignee: set_conf_assignee(2, assignee, page_id)
	elif status=='i proofed':
		if printflag: print('here5')
		set_conf_status(3, page_id)
	elif status=='assigned iip' or status=='iip in progress':
		if printflag: print('here6')
		set_conf_status(3, page_id)
		if assignee: set_conf_assignee(3, assignee, page_id)
	elif status=='ii proofed':
		if printflag: print('here7')
		set_conf_status(4, page_id)
	elif status=='assigned pr' or status=='pr in progress':
		if printflag: print('here8')
		set_conf_status(4, page_id)
		if assignee: set_conf_assignee(4, assignee, page_id)
	elif status=='proof-read':
		if printflag: print('here9')
		set_conf_status(5, page_id)
	elif status=='on hold':
		if printflag: print('here10')
		set_conf_status(6, page_id)
		# set_conf_assignee(4, assignee, page_id)


	mgr_statuses=['transcribed - mgr', 'i proofed - mgr', 'ii proofed - mgr', 'proof-read - mgr']

	if status in mgr_statuses:
		if status==mgr_statuses[0]:
			set_conf_status(2, page_id)
		elif status==mgr_statuses[1]:
			set_conf_status(3, page_id)
		elif status==mgr_statuses[2]:
			set_conf_status(4, page_id)
		elif status==mgr_statuses[3]:
			set_conf_status(5, page_id)
	newconf = get_current_conf_data(page_id)


	if is_training==0 and curconf[1]!='' and curconf[1]!=newconf[1] and curconf[1] not in removedassignee:
		removeuser(curconf[1], page_id)
		if removedassignee!='':
			removedassignee = curconf[1] + ', ' + removedassignee
		else:
			removedassignee = curconf[1]

	if success == 1:
		successString='Success'
	else:
		successString='Not Success'
	# fc.write(f'{successString}\n')
	# fc.close()

	time = datetime.datetime.now().strftime("%d-%m-%Y %H:%M:%S")
	date = datetime.datetime.now().strftime("%d-%m-%Y")
	time = datetime.datetime.now().strftime("%H:%M:%S")
	s1=[date, time
	,successString
	,"Task"
	,f'[{key} {jiratitle}]'
	,f'{res["fields"]["status"]["name"]}'
	,f'{assignee}'
	,f'{newconf[0]}'
	,f'{newconf[1]}'
	,f'{curconf[0]}'
	,f'{curconf[1]}'
	,f'{removedassignee}']
	
	while True:
		try:
			worksheet.insert_row(s1, 2)
			break
		except:
			pass

	totalupdates+=1
	return
	
def main():
	global totalupdates
	totalupdates=0
	allissues = get_jira_data()
	print(f'got {len(allissues)} from jira')
	for issue in allissues:
		try:
			update_confluence(issue)
		except:
			pass
		print('done',issue['key'])
	print('Total Tickets Updated:',totalupdates)

main()

time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
f=open(f'{linuxDir}run-log-assign.txt','a',encoding='utf-8')
f.write(f'ran on: {time}\n')
f.close()

# AT-18507

# test pid = 327683
